import { getChat, saveNew<PERSON>hat, updateChat } from "@/lib/supabase-queries";
import { checkUser } from "@/lib/supabase-server-actions";
import { Chat } from "@/lib/types";
import { createOpenAI } from "@ai-sdk/openai";
import {
  appendClientMessage,
  appendResponseMessages,
  generateId,
  type Message,
  smoothStream,
  streamText,
} from "ai";
import { generateTitle } from "@/utils/utils";

// Allow streaming responses up to 60 seconds
export const maxDuration = 60;

const lightrayChat = createOpenAI({
  baseURL: "https://api.lightray-technologies.com/api/v2",
  apiKey: "none" // Required even if not used by the API
});

const deep_research = createOpenAI({
  baseURL: "https://api.lightray-technologies.com/api/v1",
  apiKey: "none" // Required even if not used by the API
});

export async function POST(req: Request) {
  const { id, message, deepResearch }: { message: Message; id: string; deepResearch: boolean } =
    await req.json();
  const user = await checkUser();

  if (!user) {
    return new Response("Unauthorized", { status: 401 });
  }

  const chat = await getChat(id, user.id);

  if (!chat && message) {
    const title = await generateTitle(message);

    const newChat: Chat = {
      user_id: user.id,
      chat_id: id,
      title,
      messages: [message],
      created_at: new Date(),
      updated_at: new Date(),
      path: `/c/${id}`,
      share_path: `shared/live/${id}`,
      has_live_link: false,
      share_id: generateId(),
      shared_at: undefined,
      static_link_count: 0,
    };  
    await saveNewChat(newChat);
  }

  const previousMessages = chat ? chat.messages : [];
  const messages = appendClientMessage({
    messages: previousMessages,
    message,
  });

  const aiClient = deepResearch ? deep_research("deep-research") : lightrayChat("lightray");

  const result = streamText({
    model: aiClient,
    messages,
    experimental_transform: smoothStream({ chunking: "word" }),
    async onFinish(response) {
      if (response.finishReason === "stop") {
        const newMessageArray = appendResponseMessages({
          messages,
          responseMessages: response.response.messages,
        });
        await updateChat(user.id, id, newMessageArray);
      }
    },
    onError: (error) => {
      console.error("Error streaming text:", error);
    },
  });

  return result.toDataStreamResponse();
}
