import { openai } from "@ai-sdk/openai";
import { type Message, streamText } from "ai";

// Allow streaming responses up to 60 seconds
export const maxDuration = 60;

export async function POST(req: Request) {
  const { messages } = (await req.json()) as { messages: Message[] };
  const result = streamText({
    model: openai("gpt-4.1-nano"),
    messages,
  });

  return result.toDataStreamResponse();
}
