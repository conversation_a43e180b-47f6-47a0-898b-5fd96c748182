import { createOpenAI } from "@ai-sdk/openai";
import { type Message, streamText } from "ai";

// Allow streaming responses up to 60 seconds
export const maxDuration = 60;

const lightrayChat = createOpenAI({
  baseURL: "https://api.lightray-technologies.com/api/v2",
  apiKey: "none" // Required even if not used by the API
});

const deep_research = createOpenAI({
  baseURL: "https://api.lightray-technologies.com/api/v1",
  apiKey: "none" // Required even if not used by the API
});

export async function POST(req: Request) {
  const { messages, id, deepResearch } = (await req.json()) as {
    messages: Message[];
    id: string;
    deepResearch: boolean
  };

  console.log("Visitor chat request:", { messagesLength: messages?.length, id, deepResearch });

  // Validate that messages array exists and is not empty
  if (!messages || messages.length === 0) {
    console.log("Empty messages array received");
    return new Response("Messages array cannot be empty", { status: 400 });
  }

  // Temporarily force deep research to test if v1 API works
  const aiClient = deep_research("deep-research");

  console.log("Using AI client: deep-research (forced for testing)");

  const result = streamText({
    model: aiClient,
    messages,
    onError: (error) => {
      console.error("Visitor chat streaming error:", error);
    },
  });

  return result.toDataStreamResponse();
}
